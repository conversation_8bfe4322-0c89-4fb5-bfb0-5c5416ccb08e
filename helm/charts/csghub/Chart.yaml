apiVersion: v2
name: csghub
description: The main chart for CSGHub, support CE and EE.
home: https://opencsg.com
sources:
  - https://github.com/OpenCSGs/csghub-installer

# A chart can be either an 'application' or a 'library' chart.
#
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application

# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 1.8.0

# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "v1.8.0"

# Defined dependencies for subChart
dependencies:
  - name: ingress-nginx
    version: "^4.12.0"
    repository: file://charts/ingress-nginx
    condition: ingress-nginx.enabled
  - name: fluentd
    version: "^0.5.2"
    repository: file://charts/fluentd
    condition: fluentd.enabled
  - name: postgresql
    version: "^15.10"
    repository: "file://charts/postgresql"
    condition: postgresql.enabled
  - name: gitaly
    version: "^17.5.0"
    repository: "file://charts/gitaly"
    condition: gitaly.enabled
  - name: minio
    version: "^2024.7.15"
    repository: "file://charts/minio"
    condition: minio.enabled
  - name: redis
    version: "^7.2.5"
    repository: "file://charts/redis"
    condition: redis.enabled
  - name: registry
    version: "^2.8.3"
    repository: "file://charts/registry"
    condition: registry.enabled
  - name: coredns
    version: "^1.11.1"
    repository: "file://charts/coredns"
    condition: coredns.enabled
  - name: nats
    version: "^2.10.16"
    repository: "file://charts/nats"
  - name: temporal
    version: "^1.25.1"
    repository: "file://charts/temporal"
    condition: temporal.enabled
  - name: casdoor
    version: "^1.799.0"
    repository: "file://charts/casdoor"
    condition: casdoor.enabled
  - name: gitlab-shell
    version: "^17.5.0"
    repository: "file://charts/gitlab-shell"
    condition: gitlab-shell.enabled
  - name: dataflow
    version: "1.4.0"
    repository: "file://charts/dataflow"
    condition: dataflow.enabled
  - name: starship
    version: "0.1.9"
    repository: file://charts/starship
    condition: starship.enabled
  - name: watcher
    version: "1.1.1"
    repository: "file://charts/watcher"
    condition: watcher.enabled
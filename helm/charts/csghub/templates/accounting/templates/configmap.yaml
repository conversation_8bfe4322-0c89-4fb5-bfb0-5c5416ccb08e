{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
data:
  OPENCSG_ACCOUNTING_SERVER_PORT: {{ include "accounting.internal.port" . | quote }}
  OPENCSG_ACCOUNTING_FEE_EVENT_SUBJECT: "accounting.fee.>"
  OPENCSG_ACCOUNTING_NOTIFY_NOBALANCE_SUBJECT: "accounting.notify.nobalance"
  OPENCSG_ACCOUNTING_MSG_FETCH_TIMEOUTINSEC: "5"
  OPENCSG_ACCOUNTING_CHARGING_ENABLE: "true"

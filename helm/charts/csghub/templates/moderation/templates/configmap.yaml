{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.global.moderation.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  STARHUB_SERVER_SENSITIVE_CHECK_ENABLE: "true"
  STARHUB_SERVER_SENSITIVE_CHECK_ACCESS_KEY_ID: {{ .Values.global.moderation.accessKeyId }}
  STARHUB_SERVER_SENSITIVE_CHECK_ACCESS_KEY_SECRET: {{ .Values.global.moderation.accessKeySecret }}
  STARHUB_SERVER_SENSITIVE_CHECK_REGION: {{ .Values.global.moderation.region }}
{{- end }}

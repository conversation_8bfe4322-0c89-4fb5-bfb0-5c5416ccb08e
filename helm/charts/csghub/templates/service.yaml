{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated Service Template for All CSGHub Services
This template creates services for all enabled CSGHub application components
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if $serviceConfig.enabled | default true }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: 
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
  {{- with $serviceConfig.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ $serviceConfig.service.type | default "ClusterIP" }}
  ports:
    - port: {{ $serviceConfig.service.port | default 8080 }}
      targetPort: {{ $serviceConfig.service.port | default 8080 }}
      protocol: TCP
      name: {{ $serviceName }}
      {{- if and (eq ($serviceConfig.service.type | default "ClusterIP") "NodePort") $serviceConfig.service.nodePort }}
      nodePort: {{ $serviceConfig.service.nodePort }}
      {{- end }}
  selector:
    {{- include "common.labels.selector" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
{{- end }}
{{- end }}

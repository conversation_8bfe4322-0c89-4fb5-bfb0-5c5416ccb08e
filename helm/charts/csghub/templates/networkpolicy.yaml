{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated NetworkPolicy Template for All CSGHub Services
This template creates network policies for all enabled CSGHub application components
*/}}

{{- if .Values.global.networkPolicy.enabled | default false }}
{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if and ($serviceConfig.enabled | default true) ($serviceConfig.networkPolicy.enabled | default $.Values.global.networkPolicy.enabled) }}
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
spec:
  podSelector:
    matchLabels:
      {{- include "common.labels.selector" $ | nindent 6 }}
      app.kubernetes.io/component: {{ $serviceName }}
  policyTypes:
    - Ingress
    - Egress
  ingress:
    # Allow ingress from ingress controller
    - from:
        - namespaceSelector:
            matchLabels:
              name: {{ $.Values.global.networkPolicy.ingressNamespace | default "ingress-nginx" }}
      ports:
        - protocol: TCP
          port: {{ $serviceConfig.service.port | default 8080 }}
    # Allow ingress from other CSGHub services
    - from:
        - podSelector:
            matchLabels:
              {{- include "common.labels.selector" $ | nindent 14 }}
      ports:
        - protocol: TCP
          port: {{ $serviceConfig.service.port | default 8080 }}
    {{- if eq $serviceName "server" }}
    # Server needs to accept connections from all services
    - from:
        - podSelector:
            matchLabels:
              {{- include "common.labels.selector" $ | nindent 14 }}
    {{- end }}
    {{- with $serviceConfig.networkPolicy.ingress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
  egress:
    # Allow egress to DNS
    - to: []
      ports:
        - protocol: UDP
          port: 53
        - protocol: TCP
          port: 53
    # Allow egress to external services (HTTP/HTTPS)
    - to: []
      ports:
        - protocol: TCP
          port: 80
        - protocol: TCP
          port: 443
    # Allow egress to PostgreSQL
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: postgresql
      ports:
        - protocol: TCP
          port: 5432
    # Allow egress to Redis
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: redis
      ports:
        - protocol: TCP
          port: 6379
    # Allow egress to other CSGHub services
    - to:
        - podSelector:
            matchLabels:
              {{- include "common.labels.selector" $ | nindent 14 }}
    {{- if eq $serviceName "server" }}
    # Server needs egress to all infrastructure services
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: gitaly
      ports:
        - protocol: TCP
          port: 8075
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: minio
      ports:
        - protocol: TCP
          port: 9000
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: nats
      ports:
        - protocol: TCP
          port: 4222
    - to:
        - podSelector:
            matchLabels:
              app.kubernetes.io/name: temporal
      ports:
        - protocol: TCP
          port: 7233
    {{- end }}
    {{- with $serviceConfig.networkPolicy.egress }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
{{- end }}
{{- end }}
{{- end }}

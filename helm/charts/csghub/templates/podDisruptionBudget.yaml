{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated PodDisruptionBudget Template for All CSGHub Services
This template creates PDBs for all enabled CSGHub application components that have multiple replicas
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if and ($serviceConfig.enabled | default true) (gt ($serviceConfig.replicas | default 1 | int) 1) ($serviceConfig.podDisruptionBudget.enabled | default $.Values.global.podDisruptionBudget.enabled | default false) }}
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
spec:
  {{- if $serviceConfig.podDisruptionBudget.minAvailable }}
  minAvailable: {{ $serviceConfig.podDisruptionBudget.minAvailable }}
  {{- else if $serviceConfig.podDisruptionBudget.maxUnavailable }}
  maxUnavailable: {{ $serviceConfig.podDisruptionBudget.maxUnavailable }}
  {{- else }}
  {{- if gt ($serviceConfig.replicas | default 1 | int) 2 }}
  maxUnavailable: 1
  {{- else }}
  minAvailable: 1
  {{- end }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "common.labels.selector" $ | nindent 6 }}
      app.kubernetes.io/component: {{ $serviceName }}
{{- end }}
{{- end }}
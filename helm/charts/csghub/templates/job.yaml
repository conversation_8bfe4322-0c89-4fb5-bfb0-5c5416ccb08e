{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated Job Template for All CSGHub Services
This template creates jobs for all enabled CSGHub application components that have job functionality
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if and ($serviceConfig.enabled | default true) ($serviceConfig.job.enabled | default false) }}
---
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}-job
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}-job
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
    "helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
spec:
  template:
    metadata:
      labels:
        {{- include "common.labels" $ | nindent 8 }}
        app.kubernetes.io/component: {{ $serviceName }}-job
    spec:
      restartPolicy: OnFailure
      {{- with (or $.Values.global.image.pullSecrets $serviceConfig.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      initContainers:
        {{- include "common.initContainers" (dict "context" $ "config" ($serviceConfig.initContainers | default dict)) | nindent 8 }}
      containers:
        - name: {{ $serviceName }}-job
          {{- if eq $serviceName "portal" }}
          image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_portal" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
          {{- else if eq $serviceName "accounting" }}
          image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_accounting" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
          {{- else }}
          image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_server" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
          {{- end }}
          imagePullPolicy: {{ $serviceConfig.image.pullPolicy | default "IfNotPresent" }}
          {{- if $serviceConfig.job.command }}
          command: {{ $serviceConfig.job.command | toYaml | nindent 12 }}
          {{- else }}
          {{- if eq $serviceName "server" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/scripts/migrate.sh" ]
          {{- else }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub {{ $serviceName }} migrate" ]
          {{- end }}
          {{- end }}
          {{- if $serviceConfig.job.args }}
          args: {{ $serviceConfig.job.args | toYaml | nindent 12 }}
          {{- end }}
          envFrom:
            {{- if eq $serviceName "server" }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "nats") }}
            {{- if not $.Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "redis") }}
            {{- end }}
            {{- if not $.Values.global.objectStore.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "minio") }}
            {{- end }}
            {{- else }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "casdoor") }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "nats") }}
            {{- end }}
          env:
            {{- if eq $serviceName "server" }}
            {{- if not $.Values.global.redis.external }}
            - name: STARHUB_SERVER_REDIS_PASSWORD
              value: "$(REDIS_PASSWD)"
            {{- end }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- if not $.Values.global.objectStore.external }}
            - name: STARHUB_SERVER_S3_ACCESS_KEY_ID
              value: "$(MINIO_ROOT_USER)"
            - name: STARHUB_SERVER_S3_ACCESS_KEY_SECRET
              value: "$(MINIO_ROOT_PASSWORD)"
            {{- end }}
            {{- else }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- end }}
            {{- with $serviceConfig.job.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- $serviceConfig.job.resources | default $serviceConfig.resources | toYaml | nindent 12 }}
          {{- if or (eq $serviceName "server") (eq $serviceName "user") }}
          volumeMounts:
            - name: jwt-token-crt
              mountPath: /starhub-bin/casdoor
          {{- end }}
      {{- if or (eq $serviceName "server") (eq $serviceName "user") }}
      volumes:
        - name: jwt-token-crt
          secret:
            secretName: {{ include "common.names.custom" (list $ "casdoor") }}
      {{- end }}
      {{- with $serviceConfig.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}

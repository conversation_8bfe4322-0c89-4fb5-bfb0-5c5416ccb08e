{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated ServiceAccount Template for All CSGHub Services
This template creates service accounts for all enabled CSGHub application components that require them
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if and ($serviceConfig.enabled | default true) ($serviceConfig.serviceAccount.create | default false) }}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
  {{- with $serviceConfig.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
automountServiceAccountToken: {{ $serviceConfig.serviceAccount.automount | default false }}
{{- end }}
{{- end }}

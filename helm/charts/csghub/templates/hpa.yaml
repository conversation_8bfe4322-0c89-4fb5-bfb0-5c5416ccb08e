{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated HorizontalPodAutoscaler Template for All CSGHub Services
This template creates HPAs for all enabled CSGHub application components that have autoscaling enabled
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if and ($serviceConfig.enabled | default true) ($serviceConfig.autoscaler.enabled | default false) }}
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels:
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ include "common.names.custom" (list $ $serviceName) }}
  minReplicas: {{ $serviceConfig.autoscaler.minReplicas | default 1 }}
  maxReplicas: {{ $serviceConfig.autoscaler.maxReplicas | default 10 }}
  metrics:
    {{- if $serviceConfig.autoscaler.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ $serviceConfig.autoscaler.targetCPUUtilizationPercentage }}
    {{- end }}
    {{- if $serviceConfig.autoscaler.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageUtilization: {{ $serviceConfig.autoscaler.targetMemoryUtilizationPercentage }}
    {{- end }}
  {{- with $serviceConfig.autoscaler.behavior }}
  behavior:
    {{- toYaml . | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}

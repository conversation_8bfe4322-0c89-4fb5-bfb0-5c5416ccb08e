{{/*
Check deprecated
*/}}
{{ include "common.checkDeprecated" . }}

CHART NAME: {{ .Chart.Name }}
CHART VERSION: {{ .Chart.Version }}
APP VERSION: {{ .Chart.AppVersion }}

You have successfully installed CSGHub!

Visit CSGHub at the following address:
      {{ $password := include "casdoor.initPass" (durationRound now) }}
      {{- $secretData := (lookup "v1" "Secret" .Release.Namespace (include "common.names.custom" (list . "init-root"))).data }}
      {{- if $secretData }}
      {{- $secretPass := index $secretData "INIT_ROOT_PASSWORD" }}
      {{- if $secretPass }}
      {{- $password = $secretPass | b64dec }}
      {{- end }}
      {{- end }}
    Address: {{ include "csghub.external.endpoint" . }}
    Credentials: root/{{ $password }}

Visit the Casdoor administrator console at the following address:

    Address: {{ include "casdoor.external.endpoint" . }}
    Credentials:
        Username: root
        Password: $(kube<PERSON>l get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "init-root") }} -o jsonpath="{.data.INIT_ROOT_PASSWORD}" | base64 -d)

*Notes: The admin password is valid only for initial installation.*
{{ if eq (include "starship.enabled" .) "true"  }}
Visit the Starship admin console at the following address:
    Address: {{ include "starship.external.api.endpoint" . }}/admin
    Credential: admin/Admin@1234
{{- end }}

Visit the Temporal console at the following address:

    Address: {{ include "temporal.external.endpoint" . }}
    Credentials:
        Username: $(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "temporal") }} -o jsonpath="{.data.TEMPORAL_USERNAME}" | base64 -d)
        Password: $(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "temporal") }} -o jsonpath="{.data.TEMPORAL_PASSWORD}" | base64 -d)
{{ if not .Values.global.objectStore.external }}
Visit the Minio console at the following address:

    Address: {{ include "minio.external.endpoint" . }}/console/
    Credentials:
        Username: $(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "minio") }} -o jsonpath="{.data.MINIO_ROOT_USER}" | base64 -d)
        Password: $(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "minio") }} -o jsonpath="{.data.MINIO_ROOT_PASSWORD}" | base64 -d)
{{- end }}
{{ if not .Values.global.registry.external }}
To access Registry using docker-cli:
    {{ $registry := (include "registry.external.endpoint" . | trimPrefix "http://" | trimPrefix "https://") }}
    Endpoint: {{ $registry }}
    Credentials:
        Username=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "registry") }} -ojsonpath='{.data.REGISTRY_USERNAME}' | base64 -d)
        Password=$(kubectl get secret --namespace {{ .Release.Namespace }} {{ include "common.names.custom" (list . "registry") }} -ojsonpath='{.data.REGISTRY_PASSWORD}' | base64 -d)

    Login to the registry:
        echo "$Password" | docker login {{ $registry }} --username $Username ---password-stdin

    Pull/Push images:
        docker pull {{ $registry }}/test:latest
        docker push {{ $registry }}/test:latest

*Notes: This is not a container registry suitable for production environments.*
{{ end }}
For more details, visit:
    https://github.com/OpenCSGs/csghub-installer

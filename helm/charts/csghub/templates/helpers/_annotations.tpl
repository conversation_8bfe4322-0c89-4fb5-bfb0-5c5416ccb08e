{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Common deployment annotations
*/}}
{{- define "common.annotations.deployment" -}}
{{- with .Values.annotations }}
{{- toYaml . }}
{{- end }}
{{- end -}}

{{/*
Common pod annotations with automatic checksum calculation
This template automatically detects configmap.yaml and secret.yaml files
and adds appropriate checksum annotations
*/}}
{{- define "common.annotations.pod" -}}
{{- $checksums := dict -}}
{{- $configMapPath := printf "%s/configmap.yaml" $.Template.BasePath -}}
{{- $secretPath := printf "%s/secret.yaml" $.Template.BasePath -}}
{{- if $.Files.Get $configMapPath }}
{{- $_ := set $checksums "checksum/config" (include $configMapPath . | sha256sum) -}}
{{- end }}
{{- if $.Files.Get $secretPath }}
{{- $_ := set $checksums "checksum/secret" (include $secretPath . | sha256sum) -}}
{{- end }}
{{- range $key, $value := $checksums }}
{{ $key }}: {{ $value }}
{{- end }}
{{- with .Values.podAnnotations }}
{{- toYaml . }}
{{- end }}
{{- end -}}

{{/*
Simplified pod annotations with explicit checksum paths
Usage: {{ include "common.annotations.pod.checksum" (dict "context" . "configmap" true "secret" false) }}
*/}}
{{- define "common.annotations.pod.checksum" -}}
{{- $context := .context -}}
{{- if .configmap }}
checksum/config: {{ include (print $context.Template.BasePath "/configmap.yaml") $context | sha256sum }}
{{- end }}
{{- if .secret }}
checksum/secret: {{ include (print $context.Template.BasePath "/secret.yaml") $context | sha256sum }}
{{- end }}
{{- with $context.Values.podAnnotations }}
{{- toYaml . }}
{{- end }}
{{- end -}}

{{/*
Resource dependencies annotation
This is used to indicate which resources this component depends on
*/}}
{{- define "common.annotations.dependencies" -}}
{{- if .dependencies }}
resource.dependencies/deployments: |
{{- range .dependencies }}
  {{ include "common.names.custom" . }}
{{- end }}
{{- end }}
{{- end -}}

{{/*
Standard ingress annotations for nginx
*/}}
{{- define "common.annotations.ingress.nginx" -}}
nginx.ingress.kubernetes.io/enable-cors: "true"
{{- if .auth }}
nginx.ingress.kubernetes.io/auth-type: basic
nginx.ingress.kubernetes.io/auth-secret: {{ .auth.secret }}
nginx.ingress.kubernetes.io/auth-realm: {{ .auth.realm | default "Authentication Required" | quote }}
{{- end }}
{{- with .custom }}
{{- toYaml . }}
{{- end }}
{{- end -}}

{{/*
Helm hook annotations for jobs
*/}}
{{- define "common.annotations.helm.hooks" -}}
{{- if .preInstall }}
"helm.sh/hook": pre-install,pre-upgrade
{{- else if .postInstall }}
"helm.sh/hook": post-install,post-upgrade
{{- end }}
{{- if .deletePolicy }}
"helm.sh/hook-delete-policy": {{ .deletePolicy }}
{{- else }}
"helm.sh/hook-delete-policy": before-hook-creation,hook-succeeded
{{- end }}
{{- end -}}

{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Define the endpoint for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.endpoint" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.endpoint" . }} (for backward compatibility)
*/}}
{{- define "csghub.objectStore.endpoint" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $endpoint := or (include "minio.external.endpoint" $context.Subcharts.minio) (include "minio.external.endpoint" $context) }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "endpoint" -}}
{{- if $serviceConfig.objectStore.endpoint -}}
{{- $endpoint = $serviceConfig.objectStore.endpoint -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "endpoint" }}
{{- if $context.Values.global.objectStore.connection.endpoint }}
{{- $endpoint = $context.Values.global.objectStore.connection.endpoint }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- $endpoint -}}
{{- end }}

{{/*
Define the accessKey for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.accessKey" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.accessKey" . }} (for backward compatibility)
*/}}
{{- define "csghub.objectStore.accessKey" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $accessKey := "minio" }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "accessKey" -}}
{{- if $serviceConfig.objectStore.accessKey -}}
{{- $accessKey = $serviceConfig.objectStore.accessKey -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "accessKey" }}
{{- if $context.Values.global.objectStore.connection.accessKey }}
{{- $accessKey = $context.Values.global.objectStore.connection.accessKey }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- /* Fallback to legacy objectStore.accessKey for backward compatibility */ -}}
{{- if hasKey $context.Values "objectStore" -}}
{{- if hasKey $context.Values.objectStore "accessKey" -}}
{{- if $context.Values.objectStore.accessKey -}}
{{- $accessKey = $context.Values.objectStore.accessKey -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- $accessKey -}}
{{- end }}

{{/*
Define the accessSecret for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.accessSecret" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.accessSecret" . }} (for backward compatibility)
*/}}
{{- define "csghub.objectStore.accessSecret" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $accessSecret := randAlphaNum 15 }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "accessSecret" -}}
{{- if $serviceConfig.objectStore.accessSecret -}}
{{- $accessSecret = $serviceConfig.objectStore.accessSecret -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "accessSecret" }}
{{- if $context.Values.global.objectStore.connection.accessSecret }}
{{- $accessSecret = $context.Values.global.objectStore.connection.accessSecret }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- /* Fallback to legacy objectStore.accessSecret for backward compatibility */ -}}
{{- if hasKey $context.Values "objectStore" -}}
{{- if hasKey $context.Values.objectStore "accessSecret" -}}
{{- if $context.Values.objectStore.accessSecret -}}
{{- $accessSecret = $context.Values.objectStore.accessSecret -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- $accessSecret -}}
{{- end }}

{{/*
Define the region for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.region" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.region" . }} (for backward compatibility)
*/}}
{{- define "csghub.objectStore.region" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $region := (include "minio.internal.region" $context.Subcharts.minio) }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "region" -}}
{{- if $serviceConfig.objectStore.region -}}
{{- $region = $serviceConfig.objectStore.region -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "region" }}
{{- if $context.Values.global.objectStore.connection.region }}
{{- $region = $context.Values.global.objectStore.connection.region }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- $region -}}
{{- end }}

{{/*
Define the encrypt for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.encrypt" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.encrypt" . }} (for backward compatibility)
*/}}
{{- define "csghub.objectStore.encrypt" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $encrypt := (include "minio.ingress.tls.enabled" $context.Subcharts.minio) }}
{{- if eq (include "global.ingress.tls.enabled" $context) "true" }}
{{- $encrypt = "true" }}
{{- end }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "encrypt" -}}
{{- $encrypt = $serviceConfig.objectStore.encrypt | toString -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "encrypt" }}
{{- $encrypt = $context.Values.global.objectStore.connection.encrypt | toString }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- $encrypt -}}
{{- end }}

{{/*
Define the pathStyle for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.pathStyle" (dict "context" . "service" "portal") }}
       {{ include "csghub.objectStore.pathStyle" . }} (for backward compatibility with registry)
*/}}
{{- define "csghub.objectStore.pathStyle" -}}
{{- $context := . -}}
{{- $service := "" -}}
{{- if hasKey . "context" -}}
{{- $context = .context -}}
{{- $service = .service -}}
{{- end -}}

{{- $pathStyle := "true" }}

{{- /* Handle legacy registry service special case */ -}}
{{- if eq $service "registry" }}
{{- if hasKey $context.Values.registry "objectStore" -}}
{{- if hasKey $context.Values.registry.objectStore "pathStyle" -}}
{{- $pathStyle = $context.Values.registry.objectStore.pathStyle | toString -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "pathStyle" -}}
{{- $pathStyle = $serviceConfig.objectStore.pathStyle | toString -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "external" }}
{{- if $context.Values.global.objectStore.external }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "pathStyle" }}
{{- $pathStyle = $context.Values.global.objectStore.connection.pathStyle | toString }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- $pathStyle -}}
{{- end }}

{{/*
Define the bucket for csghub objectStore with service-level override support
Usage: {{ include "csghub.objectStore.bucket" (dict "context" . "service" "portal") }}
*/}}
{{- define "csghub.objectStore.bucket" -}}
{{- $context := .context -}}
{{- $service := .service -}}

{{- $bucket := printf "csghub-%s" $service }}

{{- /* Service-level configuration (highest priority) */ -}}
{{- if and $service (hasKey $context.Values "csghub") -}}
{{- if hasKey $context.Values.csghub $service -}}
{{- $serviceConfig := index $context.Values.csghub $service -}}
{{- if hasKey $serviceConfig "objectStore" -}}
{{- if hasKey $serviceConfig.objectStore "bucket" -}}
{{- if $serviceConfig.objectStore.bucket -}}
{{- $bucket = $serviceConfig.objectStore.bucket -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}
{{- end -}}

{{- /* Global configuration (medium priority) */ -}}
{{- if hasKey $context.Values.global "objectStore" }}
{{- if hasKey $context.Values.global.objectStore "connection" }}
{{- if hasKey $context.Values.global.objectStore.connection "bucket" }}
{{- if $context.Values.global.objectStore.connection.bucket }}
{{- $bucket = $context.Values.global.objectStore.connection.bucket }}
{{- end }}
{{- end }}
{{- end }}
{{- end }}

{{- $bucket -}}
{{- end }}

{{/*
Check if external object storage is enabled
Usage: {{ include "csghub.objectStore.external" . }}
*/}}
{{- define "csghub.objectStore.external" -}}
{{- $external := false }}
{{- if hasKey .Values.global "objectStore" }}
{{- if hasKey .Values.global.objectStore "external" }}
{{- $external = .Values.global.objectStore.external }}
{{- end }}
{{- end }}
{{- $external -}}
{{- end }}
{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Common initContainers template
Usage: {{ include "common.initContainers" (dict "context" . "config" .Values.server.initContainers) }}

Config structure:
initContainers:
  waitForRedis:
    enabled: true
    image: "redis:7.2.5"
    pullPolicy: "IfNotPresent"
  waitForPostgresql:
    enabled: true
    image: "opencsg/psql:latest"
    pullPolicy: "IfNotPresent"
  waitForGitaly:
    enabled: true
    image: "busybox:latest"
    pullPolicy: "IfNotPresent"
  waitForNats:
    enabled: true
    image: "busybox:latest"
    pullPolicy: "IfNotPresent"
  waitForTemporal:
    enabled: true
    image: "busybox:latest"
    pullPolicy: "IfNotPresent"
  waitForServer:
    enabled: false
    image: "busybox:latest"
    pullPolicy: "IfNotPresent"
    service: "server"
    port: 8080
*/}}

{{- define "common.initContainers" -}}
{{- $context := .context -}}
{{- $config := .config | default dict -}}

{{- /* Simplified initContainers with basic dependency checks */ -}}

{{- /* Wait for Redis */ -}}
{{- if ($config.waitForRedis | default dict).enabled | default false }}
- name: wait-for-redis
  image: {{ $context.Values.global.image.registry }}/redis:7.2.5
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until redis-cli -h {{ include "csghub.redis.host" $context }} -p {{ include "csghub.redis.port" $context }} ping; do echo 'Wait for Redis to be ready'; sleep 2; done" ]
  envFrom:
    {{- if $context.Values.global.redis.external }}
    - configMapRef:
        name: {{ include "common.names.custom" (list $context ($config.configMapName | default "server")) }}
    {{- else }}
    - secretRef:
        name: {{ include "common.names.custom" (list $context "redis") }}
    {{- end }}
  env:
    - name: REDISCLI_AUTH
      {{- if $context.Values.global.redis.external }}
      value: "$(STARHUB_SERVER_REDIS_PASSWORD)"
      {{- else }}
      value: "$(REDIS_PASSWD)"
      {{- end }}
{{- end }}

{{- /* Wait for PostgreSQL */ -}}
{{- if ($config.waitForPostgresql | default dict).enabled | default false }}
- name: wait-for-postgresql
  image: {{ $context.Values.global.image.registry }}/opencsg/psql:latest
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until pg_isready; do echo 'Wait for PostgreSQL to be ready'; sleep 2; done" ]
  envFrom:
    - configMapRef:
        name: {{ include "common.names.custom" (list $context ($config.configMapName | default "server")) }}
  env:
    - name: PGHOST
      value: "$(STARHUB_DATABASE_HOST)"
    - name: PGPORT
      value: "$(STARHUB_DATABASE_PORT)"
    - name: PGDATABASE
      value: "$(STARHUB_DATABASE_NAME)"
    - name: PGUSER
      value: "$(STARHUB_DATABASE_USERNAME)"
    - name: PGPASSWORD
      value: "$(STARHUB_DATABASE_PASSWORD)"
{{- end }}

{{- /* Wait for Gitaly */ -}}
{{- if ($config.waitForGitaly | default dict).enabled | default false }}
- name: wait-for-gitaly
  image: {{ $context.Values.global.image.registry }}/busybox:latest
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until nc -z {{ include "csghub.gitaly.host" $context }} {{ include "csghub.gitaly.port" $context }}; do echo 'Wait for gitaly to be ready'; sleep 2; done" ]
{{- end }}

{{- /* Wait for NATS */ -}}
{{- if ($config.waitForNats | default dict).enabled | default false }}
- name: wait-for-nats
  image: {{ $context.Values.global.image.registry }}/busybox:latest
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until nc -z {{ include "nats.internal.domain" $context }} {{ include "nats.internal.ports.api" $context }}; do echo 'Wait for nats to be ready'; sleep 2; done" ]
{{- end }}

{{- /* Wait for Temporal */ -}}
{{- if ($config.waitForTemporal | default dict).enabled | default false }}
- name: wait-for-temporal
  image: {{ $context.Values.global.image.registry }}/busybox:latest
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until nc -z {{ include "temporal.internal.domain" $context }} {{ include "temporal.internal.port" $context }}; do echo 'Wait for temporal to be ready'; sleep 2; done" ]
{{- end }}

{{- /* Wait for Server */ -}}
{{- if ($config.waitForServer | default dict).enabled | default false }}
- name: wait-for-server
  image: {{ $context.Values.global.image.registry }}/busybox:latest
  imagePullPolicy: IfNotPresent
  command: [ "/bin/sh", "-c", "until nc -z {{ include "common.names.custom" (list $context ($config.waitForServer.service | default "server")) }} {{ $config.waitForServer.port | default $context.Values.server.service.port | default 8080 }}; do echo 'Wait for {{ $config.waitForServer.service | default "server" }} to be ready'; sleep 2; done" ]
{{- end }}

{{- /* Custom initContainers */}}
{{- range $name, $container := ($config.custom | default dict) }}
{{- if $container.enabled | default true }}
- name: {{ $name }}
  image: {{ $context.Values.global.image.registry }}/{{ $container.image | required (printf "initContainers.custom.%s.image is required" $name) }}
  imagePullPolicy: {{ $container.pullPolicy | default "IfNotPresent" }}
  {{- if $container.command }}
  command: {{ $container.command | toYaml | nindent 4 }}
  {{- end }}
  {{- if $container.args }}
  args: {{ $container.args | toYaml | nindent 4 }}
  {{- end }}
  {{- if $container.env }}
  env: {{ $container.env | toYaml | nindent 4 }}
  {{- end }}
  {{- if $container.envFrom }}
  envFrom: {{ $container.envFrom | toYaml | nindent 4 }}
  {{- end }}
  {{- if $container.volumeMounts }}
  volumeMounts: {{ $container.volumeMounts | toYaml | nindent 4 }}
  {{- end }}
  {{- if $container.resources }}
  resources: {{ $container.resources | toYaml | nindent 4 }}
  {{- end }}
{{- end }}
{{- end }}

{{- end }}

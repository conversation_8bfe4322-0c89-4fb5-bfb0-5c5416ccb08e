{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{/*
Consolidated Deployment Template for All CSGHub Services
This template creates deployments for all enabled CSGHub application components
*/}}

{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if $serviceConfig.enabled | default true }}
---
apiVersion: {{ include "common.capabilities.deployment.apiVersion" $ }}
kind: Deployment
metadata:
  name: {{ include "common.names.custom" (list $ $serviceName) }}
  namespace: {{ $.Release.Namespace }}
  labels: 
    {{- include "common.labels" $ | nindent 4 }}
    app.kubernetes.io/component: {{ $serviceName }}
  {{- with (include "common.annotations.deployment" $) }}
  annotations:
    {{- . | nindent 4 }}
  {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "common.labels.selector" $ | nindent 6 }}
      app.kubernetes.io/component: {{ $serviceName }}
  replicas: {{ $serviceConfig.replicas | default 1 }}
  revisionHistoryLimit: 1
  minReadySeconds: 30
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") $ | sha256sum }}
        {{- with $serviceConfig.podAnnotations }}
          {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "common.labels" $ | nindent 8 }}
        app.kubernetes.io/component: {{ $serviceName }}
    spec:
      {{- with (or $.Values.global.image.pullSecrets $serviceConfig.image.pullSecrets) }}
      imagePullSecrets:
        {{- range . }}
        - name: {{ . }}
        {{- end }}
      {{- end }}
      {{- with $serviceConfig.securityContext }}
      securityContext:
        {{- . | toYaml | nindent 8 }}
      {{- end }}
      terminationGracePeriodSeconds: 10
      {{- if $serviceConfig.serviceAccount.create }}
      serviceAccountName: {{ include "common.names.custom" (list $ $serviceName) }}
      automountServiceAccountToken: {{ $serviceConfig.serviceAccount.automount }}
      {{- end }}
      initContainers:
        {{- include "common.initContainers" (dict "context" $ "config" ($serviceConfig.initContainers | default dict)) | nindent 8 }}
      containers:
        - name: {{ $serviceName }}
          {{- if eq $serviceName "portal" }}
          image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_portal" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
          {{- else }}
          image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_server" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
          {{- end }}
          imagePullPolicy: {{ $serviceConfig.image.pullPolicy | default "IfNotPresent" }}
          {{- if eq $serviceName "server" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/scripts/init.sh" ]
          {{- else if eq $serviceName "user" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub user launch" ]
          {{- else if eq $serviceName "portal" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub portal launch" ]
          {{- else if eq $serviceName "proxy" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub proxy launch" ]
          {{- else if eq $serviceName "accounting" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub accounting launch" ]
          {{- else if eq $serviceName "mirror" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub mirror launch" ]
          {{- else if eq $serviceName "aigateway" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub aigateway launch" ]
          {{- else if eq $serviceName "dataviewer" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub dataviewer launch" ]
          {{- else if eq $serviceName "moderation" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub moderation launch" ]
          {{- else if eq $serviceName "notification" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub notification launch" ]
          {{- else if eq $serviceName "runner" }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub runner launch" ]
          {{- else }}
          command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub {{ $serviceName }} launch" ]
          {{- end }}
          ports:
            - containerPort: {{ $serviceConfig.service.port | default 8080 }}
              name: {{ $serviceName }}
              protocol: TCP
          envFrom:
            {{- if eq $serviceName "server" }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "nats") }}
            {{- if not $.Values.global.redis.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "redis") }}
            {{- end }}
            {{- if not $.Values.global.objectStore.external }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "minio") }}
            {{- end }}
            {{- else }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "casdoor") }}
            - configMapRef:
                name: {{ include "common.names.custom" (list $ "server") }}
            - secretRef:
                name: {{ include "common.names.custom" (list $ "nats") }}
            {{- end }}
          env:
            {{- if eq $serviceName "server" }}
            {{- if not $.Values.global.redis.external }}
            - name: STARHUB_SERVER_REDIS_PASSWORD
              value: "$(REDIS_PASSWD)"
            {{- end }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- if not $.Values.global.objectStore.external }}
            - name: STARHUB_SERVER_S3_ACCESS_KEY_ID
              value: "$(MINIO_ROOT_USER)"
            - name: STARHUB_SERVER_S3_ACCESS_KEY_SECRET
              value: "$(MINIO_ROOT_PASSWORD)"
            {{- end }}
            {{- else if eq $serviceName "user" }}
            - name: OPENCSG_USER_SERVER_PORT
              value: "{{ $serviceConfig.service.port | default 8080 }}"
            - name: OPENCSG_USER_SERVER_SIGNIN_SUCCESS_REDIRECT_URL
              value: {{ include "server.callback.user" $ }}
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- else if eq $serviceName "portal" }}
            - name: OPENCSG_PORTAL_SERVER_PORT
              value: "{{ $serviceConfig.service.port | default 8090 }}"
            {{- else if eq $serviceName "proxy" }}
            - name: OPENCSG_PROXY_SERVER_PORT
              value: "{{ $serviceConfig.service.port | default 8083 }}"
            {{- else if eq $serviceName "accounting" }}
            - name: OPENCSG_ACCOUNTING_SERVER_PORT
              value: "{{ $serviceConfig.service.port | default 8086 }}"
            - name: OPENCSG_ACCOUNTING_NATS_URL
              value: "nats://$(NATS_USERNAME):$(NATS_PASSWORD)@{{ include "nats.internal.domain" $ }}:{{ include "nats.internal.ports.api" $ }}"
            {{- else }}
            - name: OPENCSG_{{ $serviceName | upper }}_SERVER_PORT
              value: "{{ $serviceConfig.service.port | default 8080 }}"
            {{- end }}
            {{- with $serviceConfig.environments }}
            {{- range $key, $value := . }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- end }}
          resources:
            {{- $serviceConfig.resources | toYaml | nindent 12 }}
          livenessProbe:
            tcpSocket:
              port: {{ $serviceConfig.service.port | default 8080 }}
            initialDelaySeconds: 20
            periodSeconds: 10
          readinessProbe:
            {{- if eq $serviceName "server" }}
            httpGet:
              path: /api/v1/health
              port: {{ $serviceConfig.service.port | default 8080 }}
            {{- else if eq $serviceName "user" }}
            httpGet:
              path: /health
              port: {{ $serviceConfig.service.port | default 8080 }}
            {{- else }}
            tcpSocket:
              port: {{ $serviceConfig.service.port | default 8080 }}
            {{- end }}
            initialDelaySeconds: 10
            periodSeconds: 5
          securityContext:
            {{- $serviceConfig.podSecurityContext | toYaml | nindent 12 }}
          {{- if or (eq $serviceName "server") (eq $serviceName "user") }}
          volumeMounts:
            - name: jwt-token-crt
              mountPath: /starhub-bin/casdoor
          {{- end }}
      {{- if or (eq $serviceName "server") (eq $serviceName "user") }}
      volumes:
        - name: jwt-token-crt
          secret:
            secretName: {{ include "common.names.custom" (list $ "casdoor") }}
      {{- end }}
      {{- with $serviceConfig.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with $serviceConfig.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
{{- end }}

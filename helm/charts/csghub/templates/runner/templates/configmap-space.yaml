{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if and .Values.global.deploy.enabled .Values.global.deploy.autoConfigure }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" (list . "runner-space") }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
data:
  space-app.yaml: |
    apiVersion: v1
    kind: Namespace
    metadata:
      name: {{ .Values.global.deploy.namespace }}
      labels:
        kubernetes.io/metadata.name: {{ .Values.global.deploy.namespace }}
    ---
    apiVersion: v1
    kind: Secret
    metadata:
      name: {{ include "common.names.custom" (list . "registry-docker-config") }}
      namespace: {{ .Values.global.deploy.namespace }}
      annotations:
        helm.sh/resource-policy: keep
    type: kubernetes.io/dockerconfigjson
    data:
      {{- if .Values.global.registry.external }}
      {{- with .Values.global.registry.connection }}
      .dockerconfigjson: {{ printf "{\"auths\":{\"%s\":{\"username\":\"%s\",\"password\":\"%s\",\"auth\":\"%s\"}}}" .repository .username .password (printf "%s:%s" .username .password | b64enc) | b64enc }}
      {{- end }}
      {{- else }}
      {{- $registry := (include "registry.external.endpoint" . | trimPrefix "http://" | trimPrefix "https://") -}}
      {{- $secretData := (lookup "v1" "Secret" .Release.Namespace (include "common.names.custom" (list . "registry"))).data -}}
      {{- $username := include "csghub.registry.username" . -}}
      {{- $password := include "csghub.registry.password" . -}}
      {{- if $secretData }}
      {{- $secretUser := index $secretData "REGISTRY_USERNAME" -}}
      {{- if $secretUser }}
      {{- $username = $secretUser | b64dec }}
      {{- end }}
      {{- $secretPass := index $secretData "REGISTRY_PASSWORD" -}}
      {{- if $secretPass }}
      {{- $password = $secretPass | b64dec }}
      {{- end }}
      {{- end }}
      .dockerconfigjson: {{ printf "{\"auths\":{\"%s\":{\"username\":\"%s\",\"password\":\"%s\",\"auth\":\"%s\"}}}" $registry $username $password (printf "%s:%s" $username $password | b64enc) | b64enc }}
      {{- end }}
    ---
{{- end }}
# Example configuration for object storage optimization
# This file demonstrates how to configure object storage with the new unified system

global:
  # Global object storage configuration
  objectStore:
    # Set to true to use external object storage, false to use built-in minio
    external: true
    
    # Global connection settings - these will be used as defaults for all services
    connection:
      # External S3-compatible object storage endpoint
      endpoint: "https://s3.amazonaws.com"
      # Access credentials
      accessKey: "your-access-key"
      accessSecret: "your-secret-key"
      # AWS region
      region: "us-west-2"
      # Use path-style URLs (true for minio, false for AWS S3)
      pathStyle: false
      # Enable TLS encryption
      encrypt: true
      # Default bucket (can be overridden per service)
      bucket: "csghub-default"

# Service-specific configurations
csghub:
  # Portal service configuration
  portal:
    objectStore:
      # Override global settings for portal service
      bucket: "csghub-portal-prod"
      # All other settings inherit from global.objectStore.connection
      
  # Server service configuration  
  server:
    objectStore:
      # Use a different bucket for server
      bucket: "csghub-server-prod"
      # Override region for server service
      region: "us-east-1"
      # All other settings inherit from global.objectStore.connection
      
  # Registry service configuration
  registry:
    objectStore:
      # Registry needs its own bucket
      bucket: "csghub-registry-prod"
      # Registry might need path-style URLs even with AWS S3
      pathStyle: true
      # All other settings inherit from global.objectStore.connection
      
  # Workflow service configuration
  workflow:
    objectStore:
      # Workflow service bucket
      bucket: "csghub-workflow-prod"
      # All other settings inherit from global.objectStore.connection

---
# Alternative configuration: Using built-in minio with service-specific overrides
global:
  objectStore:
    # Use built-in minio
    external: false
    # No connection settings needed for internal minio

csghub:
  # Even with internal minio, you can override specific settings per service
  portal:
    objectStore:
      # Custom bucket name for portal
      bucket: "portal-data"
      
  server:
    objectStore:
      # Custom bucket name for server
      bucket: "server-data"
      
  registry:
    objectStore:
      # Custom bucket name for registry
      bucket: "registry-data"
      
  workflow:
    objectStore:
      # Custom bucket name for workflow
      bucket: "workflow-data"

---
# Mixed configuration: External storage with some services using different settings
global:
  objectStore:
    external: true
    connection:
      endpoint: "https://s3.amazonaws.com"
      accessKey: "global-access-key"
      accessSecret: "global-secret-key"
      region: "us-west-2"
      pathStyle: false
      encrypt: true
      bucket: "csghub-default"

csghub:
  # Portal uses global settings
  portal:
    objectStore: {}
    
  # Server uses different credentials and region
  server:
    objectStore:
      accessKey: "server-specific-key"
      accessSecret: "server-specific-secret"
      region: "eu-west-1"
      bucket: "csghub-server-eu"
      
  # Registry uses a completely different S3-compatible service
  registry:
    objectStore:
      endpoint: "https://minio.company.com:9000"
      accessKey: "registry-key"
      accessSecret: "registry-secret"
      region: "local"
      pathStyle: true
      encrypt: false
      bucket: "company-registry"
      
  # Workflow uses global settings but with custom bucket
  workflow:
    objectStore:
      bucket: "csghub-workflows"

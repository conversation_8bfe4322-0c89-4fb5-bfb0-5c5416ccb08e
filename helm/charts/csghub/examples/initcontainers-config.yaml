# CSGHub InitContainers Configuration Examples
# This file demonstrates how to configure initContainers for different services

# Global initContainers configuration
# These settings apply to all services unless overridden at the service level
global:
  initContainers:
    # Wait for Redis to be ready
    waitForRedis:
      enabled: true
      image: "redis:7.2.5"
      pullPolicy: "IfNotPresent"
    
    # Wait for PostgreSQL to be ready
    waitForPostgresql:
      enabled: true
      image: "opencsg/psql:latest"
      pullPolicy: "IfNotPresent"
    
    # Wait for Gitaly to be ready
    waitForGitaly:
      enabled: true
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"
    
    # Wait for NATS to be ready
    waitForNats:
      enabled: true
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"
    
    # Wait for Temporal to be ready
    waitForTemporal:
      enabled: true
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"
    
    # Wait for Server to be ready (disabled by default)
    waitForServer:
      enabled: false
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"
      service: "server"
      port: 8080
    
    # Wait for Minio to be ready (disabled by default)
    waitForMinio:
      enabled: false
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"
    
    # Wait for Casdoor to be ready (disabled by default)
    waitForCasdoor:
      enabled: false
      image: "busybox:latest"
      pullPolicy: "IfNotPresent"

# Service-specific initContainers configuration
# Each service can override global settings or add custom initContainers

# Server service - needs all infrastructure dependencies
server:
  initContainers:
    configMapName: "server"
    # All infrastructure dependencies enabled (inherits from global)
    # No overrides needed for server

# User service - depends on server, not directly on infrastructure
user:
  initContainers:
    configMapName: "server"  # Use server configmap for database connection
    # Enable waiting for server to be ready
    waitForServer:
      enabled: true
      service: "server"
      port: 8080
    # Disable infrastructure dependencies (server handles them)
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false

# Portal service - only needs server
portal:
  initContainers:
    configMapName: "server"
    waitForServer:
      enabled: true
    # Disable all infrastructure dependencies
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false

# Proxy service - needs server and potentially other services
proxy:
  initContainers:
    configMapName: "server"
    waitForServer:
      enabled: true
    # Custom initContainer example
    custom:
      waitForPortal:
        enabled: true
        image: "busybox:latest"
        pullPolicy: "IfNotPresent"
        command: ["/bin/sh", "-c"]
        args: ["until nc -z csghub-portal 8090; do echo 'Wait for portal'; sleep 2; done"]

# Accounting service - needs database and NATS
accounting:
  initContainers:
    configMapName: "server"
    # Only enable what's needed
    waitForPostgresql:
      enabled: true
    waitForNats:
      enabled: true
    # Disable others
    waitForRedis:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForTemporal:
      enabled: false

# Example: Service with completely custom initContainers
customService:
  initContainers:
    # Disable all standard initContainers
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false
    waitForServer:
      enabled: false
    
    # Define custom initContainers
    custom:
      setupDatabase:
        enabled: true
        image: "postgres:15"
        pullPolicy: "IfNotPresent"
        command: ["/bin/sh", "-c"]
        args: ["psql -h $DB_HOST -U $DB_USER -c 'CREATE DATABASE IF NOT EXISTS mydb;'"]
        env:
          - name: DB_HOST
            value: "postgresql"
          - name: DB_USER
            value: "postgres"
        envFrom:
          - secretRef:
              name: "database-credentials"
      
      downloadAssets:
        enabled: true
        image: "alpine/curl:latest"
        pullPolicy: "IfNotPresent"
        command: ["/bin/sh", "-c"]
        args: ["curl -o /shared/config.json https://example.com/config.json"]
        volumeMounts:
          - name: shared-data
            mountPath: /shared
        resources:
          limits:
            cpu: 100m
            memory: 128Mi
          requests:
            cpu: 50m
            memory: 64Mi

# Example: Minimal configuration for development
development:
  global:
    initContainers:
      # Disable all initContainers for faster startup in development
      waitForRedis:
        enabled: false
      waitForPostgresql:
        enabled: false
      waitForGitaly:
        enabled: false
      waitForNats:
        enabled: false
      waitForTemporal:
        enabled: false

# Example: Production configuration with all dependencies
production:
  global:
    initContainers:
      # Enable all dependencies with production-ready images
      waitForRedis:
        enabled: true
        image: "redis:7.2.5-alpine"
        pullPolicy: "Always"
      waitForPostgresql:
        enabled: true
        image: "postgres:15-alpine"
        pullPolicy: "Always"
      waitForGitaly:
        enabled: true
        image: "busybox:1.36-musl"
        pullPolicy: "Always"
      waitForNats:
        enabled: true
        image: "busybox:1.36-musl"
        pullPolicy: "Always"
      waitForTemporal:
        enabled: true
        image: "busybox:1.36-musl"
        pullPolicy: "Always"

# CSGHub InitContainers Configuration Guide

## Overview

The CSGHub Helm chart provides a flexible and reusable initContainers template system that allows you to control which initialization containers are used for each service. This eliminates code duplication and provides consistent dependency management across all services.

## Features

- **Centralized Configuration**: Define initContainers globally and override per service
- **Conditional Enablement**: Enable/disable specific initContainers based on your deployment needs
- **Custom InitContainers**: Add service-specific initialization logic
- **Consistent Naming**: Standardized naming conventions across all services
- **Resource Optimization**: Only run necessary initialization containers

## Template Usage

The initContainers template is used in deployment templates like this:

```yaml
initContainers:
  {{- include "common.initContainers" (dict "context" . "config" (.Values.initContainers | default dict)) | nindent 8 }}
```

## Configuration Structure

### Global Configuration

Set default initContainers configuration that applies to all services:

```yaml
global:
  initContainers:
    waitForRedis:
      enabled: true
      image: "redis:7.2.5"
      pullPolicy: "IfNotPresent"
    waitForPostgresql:
      enabled: true
      image: "opencsg/psql:latest"
      pullPolicy: "IfNotPresent"
    # ... other initContainers
```

### Service-Level Configuration

Override global settings or add service-specific configuration:

```yaml
server:
  initContainers:
    configMapName: "server"  # ConfigMap name for environment variables
    # Override specific initContainers
    waitForRedis:
      enabled: true
      image: "redis:7.2.5-alpine"  # Different image
    # Add custom initContainers
    custom:
      setupStorage:
        enabled: true
        image: "busybox:latest"
        command: ["/bin/sh", "-c"]
        args: ["mkdir -p /data && chown 1000:1000 /data"]
```

## Available InitContainers

### Standard InitContainers

| Name | Purpose | Default Image | Default Enabled |
|------|---------|---------------|-----------------|
| `waitForRedis` | Wait for Redis to be ready | `redis:7.2.5` | `true` |
| `waitForPostgresql` | Wait for PostgreSQL to be ready | `opencsg/psql:latest` | `true` |
| `waitForGitaly` | Wait for Gitaly to be ready | `busybox:latest` | `true` |
| `waitForNats` | Wait for NATS to be ready | `busybox:latest` | `true` |
| `waitForTemporal` | Wait for Temporal to be ready | `busybox:latest` | `true` |
| `waitForServer` | Wait for CSGHub server to be ready | `busybox:latest` | `false` |
| `waitForMinio` | Wait for Minio to be ready | `busybox:latest` | `false` |
| `waitForCasdoor` | Wait for Casdoor to be ready | `busybox:latest` | `false` |

### Custom InitContainers

You can define custom initContainers under the `custom` key:

```yaml
initContainers:
  custom:
    myCustomInit:
      enabled: true
      image: "alpine:latest"
      pullPolicy: "IfNotPresent"
      command: ["/bin/sh", "-c"]
      args: ["echo 'Custom initialization'"]
      env:
        - name: CUSTOM_VAR
          value: "custom_value"
      resources:
        limits:
          cpu: 100m
          memory: 128Mi
```

## Common Configuration Patterns

### 1. Server Service (Full Dependencies)

The server service typically needs all infrastructure dependencies:

```yaml
server:
  initContainers:
    configMapName: "server"
    # All infrastructure dependencies enabled by default
    # No overrides needed
```

### 2. Dependent Services (Wait for Server)

Services that depend on the server but not directly on infrastructure:

```yaml
user:
  initContainers:
    configMapName: "server"
    waitForServer:
      enabled: true
      service: "server"
      port: 8080
    # Disable infrastructure dependencies
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false
```

### 3. Minimal Dependencies

For services with specific dependency requirements:

```yaml
accounting:
  initContainers:
    configMapName: "server"
    # Only enable what's needed
    waitForPostgresql:
      enabled: true
    waitForNats:
      enabled: true
    # Disable others
    waitForRedis:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForTemporal:
      enabled: false
```

### 4. Development Environment

Disable all initContainers for faster development cycles:

```yaml
global:
  initContainers:
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false
```

## Configuration Parameters

### Standard Parameters

Each initContainer supports these parameters:

- `enabled`: Boolean to enable/disable the initContainer
- `image`: Container image to use
- `pullPolicy`: Image pull policy (`IfNotPresent`, `Always`, `Never`)

### Service-Specific Parameters

Some initContainers have additional parameters:

#### waitForServer
- `service`: Name of the service to wait for (default: "server")
- `port`: Port number to check (default: 8080)

### Custom InitContainer Parameters

Custom initContainers support all standard Kubernetes container fields:

- `command`: Container command
- `args`: Container arguments
- `env`: Environment variables
- `envFrom`: Environment from ConfigMaps/Secrets
- `volumeMounts`: Volume mounts
- `resources`: Resource limits and requests

## Best Practices

### 1. Use Appropriate Dependencies

Only enable initContainers that your service actually needs:

```yaml
# Good: Only wait for what's needed
portal:
  initContainers:
    waitForServer:
      enabled: true
    waitForRedis:
      enabled: false  # Portal doesn't use Redis directly

# Bad: Waiting for everything
portal:
  initContainers:
    # All enabled by default - unnecessary delays
```

### 2. Configure ConfigMap Names

Specify the correct configMap name for environment variables:

```yaml
user:
  initContainers:
    configMapName: "server"  # Use server's configmap for DB connection
```

### 3. Use Consistent Images

Use consistent, lightweight images for better performance:

```yaml
global:
  initContainers:
    waitForRedis:
      image: "redis:7.2.5-alpine"  # Alpine for smaller size
    waitForPostgresql:
      image: "postgres:15-alpine"
```

### 4. Resource Limits

Set appropriate resource limits for custom initContainers:

```yaml
custom:
  myInit:
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
      requests:
        cpu: 50m
        memory: 64Mi
```

## Troubleshooting

### InitContainer Stuck

If an initContainer is stuck waiting:

1. Check if the target service is running:
   ```bash
   kubectl get pods -l app.kubernetes.io/component=redis
   ```

2. Check initContainer logs:
   ```bash
   kubectl logs <pod-name> -c wait-for-redis
   ```

3. Verify network connectivity:
   ```bash
   kubectl exec <pod-name> -c wait-for-redis -- nc -z redis-service 6379
   ```

### Configuration Issues

1. Validate your configuration:
   ```bash
   helm template . --debug --show-only templates/server/templates/deployment.yaml
   ```

2. Check for syntax errors in custom initContainers

3. Verify configMap names and references

## Migration from Old Structure

If you're migrating from the old hardcoded initContainers:

1. Remove old initContainer definitions from deployment templates
2. Add the new template include
3. Configure initContainers in values.yaml
4. Test the deployment

Example migration:

```yaml
# Old (in deployment template)
initContainers:
  - name: wait-for-redis
    image: redis:7.2.5
    # ... hardcoded configuration

# New (in deployment template)
initContainers:
  {{- include "common.initContainers" (dict "context" . "config" (.Values.initContainers | default dict)) | nindent 8 }}

# New (in values.yaml)
server:
  initContainers:
    waitForRedis:
      enabled: true
      image: "redis:7.2.5"
```

## Examples

See `examples/initcontainers-config.yaml` for complete configuration examples covering various scenarios including development, production, and custom service configurations.

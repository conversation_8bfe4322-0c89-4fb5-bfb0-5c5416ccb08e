# Object Storage Configuration Guide

This document describes the unified object storage configuration system for CSGHub services.

## Overview

The object storage configuration system supports a hierarchical configuration approach with the following priority levels:

1. **Service-level configuration** (highest priority): `csghub.<service>.objectStore`
2. **Global configuration** (medium priority): `global.objectStore.connection`
3. **Default values** (lowest priority): Built-in defaults or minio configuration

## Supported Services

The following services support object storage configuration:

- `portal` - Portal service
- `server` - Server service  
- `registry` - Registry service
- `workflow` - Workflow service (via temporal)

## Configuration Structure

### Global Configuration

```yaml
global:
  objectStore:
    # Enable/disable external object storage
    external: true|false
    
    # Global connection settings (used as defaults)
    connection:
      endpoint: "https://s3.amazonaws.com"
      accessKey: "your-access-key"
      accessSecret: "your-secret-key"
      region: "us-west-2"
      pathStyle: true|false
      encrypt: true|false
      bucket: "default-bucket"
```

### Service-level Configuration

```yaml
csghub:
  <service-name>:
    objectStore:
      endpoint: ""          # Override global endpoint
      accessKey: ""         # Override global accessKey
      accessSecret: ""      # Override global accessSecret
      region: ""            # Override global region
      pathStyle: ""         # Override global pathStyle
      encrypt: ""           # Override global encrypt
      bucket: ""            # Override global bucket
```

## Configuration Parameters

| Parameter | Description | Default | Example |
|-----------|-------------|---------|---------|
| `endpoint` | Object storage endpoint URL | minio endpoint | `https://s3.amazonaws.com` |
| `accessKey` | Access key for authentication | `minio` | `AKIAIOSFODNN7EXAMPLE` |
| `accessSecret` | Secret key for authentication | random | `wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY` |
| `region` | Storage region | `cn-north-1` | `us-west-2` |
| `pathStyle` | Use path-style URLs | `true` | `true` for minio, `false` for AWS S3 |
| `encrypt` | Enable TLS encryption | `false` | `true` for HTTPS |
| `bucket` | Bucket name | `csghub-<service>` | `my-custom-bucket` |

## Usage Examples

### Example 1: External S3 with Global Settings

```yaml
global:
  objectStore:
    external: true
    connection:
      endpoint: "https://s3.amazonaws.com"
      accessKey: "AKIAIOSFODNN7EXAMPLE"
      accessSecret: "wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY"
      region: "us-west-2"
      pathStyle: false
      encrypt: true

# All services will use the global settings with default bucket names:
# - portal: csghub-portal
# - server: csghub-server  
# - registry: csghub-registry
# - workflow: csghub-workflow
```

### Example 2: Service-specific Overrides

```yaml
global:
  objectStore:
    external: true
    connection:
      endpoint: "https://s3.amazonaws.com"
      accessKey: "global-key"
      accessSecret: "global-secret"
      region: "us-west-2"
      pathStyle: false
      encrypt: true

csghub:
  portal:
    objectStore:
      bucket: "portal-production"
      
  server:
    objectStore:
      region: "eu-west-1"
      bucket: "server-eu"
      
  registry:
    objectStore:
      endpoint: "https://minio.company.com:9000"
      accessKey: "registry-key"
      accessSecret: "registry-secret"
      pathStyle: true
      encrypt: false
      bucket: "company-registry"
```

### Example 3: Built-in Minio with Custom Buckets

```yaml
global:
  objectStore:
    external: false  # Use built-in minio

csghub:
  portal:
    objectStore:
      bucket: "portal-data"
      
  server:
    objectStore:
      bucket: "server-data"
```

## Migration from Legacy Configuration

If you're upgrading from a previous version, the system maintains backward compatibility:

- Legacy `objectStore.accessKey` configurations are still supported
- Existing service-specific configurations in individual service values files continue to work
- The new system provides additional flexibility while maintaining existing functionality

## Best Practices

1. **Use global configuration** for common settings across all services
2. **Override at service level** only when services need different configurations
3. **Keep credentials secure** using Kubernetes secrets or external secret management
4. **Use appropriate pathStyle** settings: `true` for minio, `false` for AWS S3
5. **Enable encryption** (`encrypt: true`) for production environments
6. **Use descriptive bucket names** to identify service data easily

## Troubleshooting

### Common Issues

1. **Service can't connect to object storage**
   - Check endpoint URL format (include protocol: http:// or https://)
   - Verify credentials are correct
   - Ensure bucket exists and is accessible

2. **Path-style URL issues**
   - Use `pathStyle: true` for minio and self-hosted S3-compatible services
   - Use `pathStyle: false` for AWS S3 and most cloud providers

3. **TLS/SSL connection errors**
   - Set `encrypt: true` for HTTPS endpoints
   - Set `encrypt: false` for HTTP endpoints
   - Verify certificate validity for HTTPS endpoints

### Debugging

To debug object storage configuration, check the generated ConfigMaps:

```bash
kubectl get configmap <service-name> -o yaml
```

Look for the S3/object storage environment variables to verify the configuration is applied correctly.

# CSGHub Template Consolidation Guide

## Overview

The CSGHub Helm chart has been optimized by consolidating distributed service-specific templates into unified template files. This approach reduces complexity, improves maintainability, and provides consistent configuration across all services.

## New Template Structure

### Consolidated Templates

| Template File | Purpose | Services Covered |
|---------------|---------|------------------|
| `templates/deployment.yaml` | All service deployments | server, user, portal, proxy, accounting, mirror, aigateway, dataviewer, moderation, notification, runner |
| `templates/service.yaml` | All service services | All CSGHub services |
| `templates/serviceaccount.yaml` | All service accounts | Services with `serviceAccount.create: true` |
| `templates/hpa.yaml` | Horizontal Pod Autoscalers | Services with `autoscaler.enabled: true` |
| `templates/job.yaml` | Jobs and migrations | Services with `job.enabled: true` |
| `templates/networkpolicy.yaml` | Network policies | Services with network policy enabled |
| `templates/poddisruptionbudget.yaml` | Pod disruption budgets | Services with multiple replicas |

### Template Logic

Each consolidated template uses a loop to iterate through all services:

```yaml
{{- $services := list "server" "user" "portal" "proxy" "accounting" "mirror" "aigateway" "dataviewer" "moderation" "notification" "runner" -}}

{{- range $serviceName := $services }}
{{- $serviceConfig := index $.Values $serviceName }}
{{- if $serviceConfig.enabled | default true }}
---
# Service-specific resource definition
{{- end }}
{{- end }}
```

## Configuration Structure

### Service Configuration

Each service now supports a comprehensive configuration structure:

```yaml
serviceName:  # e.g., server, user, portal, etc.
  enabled: true
  replicas: 1
  
  image:
    repository: "csghub_server"
    pullPolicy: "IfNotPresent"
    pullSecrets: []
  
  service:
    type: ClusterIP
    port: 8080
    annotations: {}
    nodePort: null  # For NodePort services
  
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 200m
      memory: 512Mi
  
  environments: {}  # Additional environment variables
  
  # Kubernetes scheduling
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  # Security
  podSecurityContext: {}
  securityContext: {}
  
  # Service account
  serviceAccount:
    create: false
    automount: false
    annotations: {}
  
  # Auto-scaling
  autoscaler:
    enabled: false
    minReplicas: 1
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
    behavior: {}
  
  # Jobs
  job:
    enabled: false
    command: []
    args: []
    environments: {}
    resources: {}
  
  # Network policies
  networkPolicy:
    enabled: false
    ingress: []
    egress: []
  
  # Pod disruption budgets
  podDisruptionBudget:
    enabled: false
    minAvailable: null
    maxUnavailable: null
  
  # Init containers
  initContainers:
    configMapName: "server"
    # Inherits from global.initContainers
```

## Service-Specific Configurations

### Server Service

The server service is the core component and requires all infrastructure dependencies:

```yaml
server:
  enabled: true
  replicas: 1
  service:
    port: 8080
  initContainers:
    configMapName: "server"
    # All infrastructure dependencies enabled by default
```

### User Service

The user service depends on the server but not directly on infrastructure:

```yaml
user:
  enabled: true
  service:
    port: 8080
  initContainers:
    configMapName: "server"
    waitForServer:
      enabled: true
      service: "server"
      port: 8080
    # Infrastructure dependencies disabled
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
```

### Portal Service

The portal service is the web frontend:

```yaml
portal:
  enabled: true
  service:
    port: 8090
  image:
    repository: "csghub_portal"
```

## Template Features

### 1. Conditional Resource Creation

Resources are only created for enabled services:

```yaml
{{- if $serviceConfig.enabled | default true }}
# Resource definition
{{- end }}
```

### 2. Service-Specific Image Selection

Different services use different container images:

```yaml
{{- if eq $serviceName "portal" }}
image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_portal" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
{{- else if eq $serviceName "accounting" }}
image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_accounting" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
{{- else }}
image: {{ $.Values.global.image.registry }}/{{ $serviceConfig.image.repository | default "csghub_server" }}:{{ include "csghub.image.tag" (dict "tag" $.Values.global.image.tag "context" $) }}
{{- end }}
```

### 3. Service-Specific Commands

Each service has its own startup command:

```yaml
{{- if eq $serviceName "server" }}
command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/scripts/init.sh" ]
{{- else if eq $serviceName "user" }}
command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub user launch" ]
{{- else if eq $serviceName "portal" }}
command: [ "/bin/sh", "-c", "update-ca-certificates && /starhub-bin/starhub portal launch" ]
{{- end }}
```

### 4. Flexible Environment Configuration

Services can have different environment variable configurations:

```yaml
{{- if eq $serviceName "server" }}
# Server-specific environment variables
{{- else if eq $serviceName "user" }}
- name: OPENCSG_USER_SERVER_PORT
  value: "{{ $serviceConfig.service.port | default 8080 }}"
{{- else }}
- name: OPENCSG_{{ $serviceName | upper }}_SERVER_PORT
  value: "{{ $serviceConfig.service.port | default 8080 }}"
{{- end }}
```

## Benefits

### 1. Reduced Complexity
- Single template files instead of distributed templates across service directories
- Easier to understand and maintain the overall chart structure
- Reduced number of files to manage

### 2. Consistent Configuration
- Unified approach to service configuration
- Consistent naming conventions across all services
- Standardized resource definitions

### 3. Better Performance
- Fewer template files for Helm to process
- Faster chart rendering and deployment
- Reduced memory usage during template processing

### 4. Easier Maintenance
- Centralized template logic
- Single place to update common patterns
- Easier to add new services or modify existing ones

### 5. Enhanced Flexibility
- Easy to enable/disable services
- Consistent configuration options across all services
- Simplified values structure

## Migration Guide

### From Old Structure

If you're migrating from the old distributed template structure:

1. **Backup existing configuration**:
   ```bash
   helm get values csghub > old-values.yaml
   ```

2. **Update values structure**:
   - Service configurations remain largely the same
   - Some new options are available (autoscaler, networkPolicy, etc.)

3. **Test the new templates**:
   ```bash
   helm template csghub . -f old-values.yaml
   ```

4. **Deploy with new structure**:
   ```bash
   helm upgrade csghub . -f old-values.yaml
   ```

### Configuration Changes

Most existing configurations remain compatible. New options include:

- `autoscaler` configuration for HPA
- `networkPolicy` configuration for network policies
- `podDisruptionBudget` configuration for PDBs
- `job` configuration for jobs and migrations

## Troubleshooting

### Template Rendering Issues

1. **Check service configuration**:
   ```bash
   helm template csghub . --debug --show-only templates/deployment.yaml
   ```

2. **Validate values**:
   ```bash
   helm lint . -f your-values.yaml
   ```

3. **Test specific services**:
   ```yaml
   # Disable all services except one for testing
   server:
     enabled: true
   user:
     enabled: false
   # ... disable others
   ```

### Common Issues

1. **Missing service configuration**: Ensure each service has at least basic configuration in values.yaml
2. **Image repository conflicts**: Check that image repositories are correctly specified
3. **Port conflicts**: Verify that service ports don't conflict
4. **Resource limits**: Ensure resource limits are properly formatted

## Best Practices

### 1. Service Configuration
- Always specify resource limits and requests
- Use appropriate service types (ClusterIP for internal services)
- Configure health checks appropriately

### 2. Scaling Configuration
- Enable autoscaling for services that need it
- Set appropriate min/max replicas
- Configure pod disruption budgets for critical services

### 3. Security Configuration
- Use network policies in production
- Configure appropriate security contexts
- Limit service account permissions

### 4. Monitoring and Observability
- Configure appropriate labels for monitoring
- Use consistent naming conventions
- Add annotations for external tools

## Examples

See the `examples/` directory for complete configuration examples covering various deployment scenarios.

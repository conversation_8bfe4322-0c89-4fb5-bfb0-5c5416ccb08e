## All modifications in this configuration file are only performed under global configuration block.
## Any configuration outside global configuration block is an automatic adaptation configuration and should not be modified.

## Global configuration will override <PERSON><PERSON><PERSON>'s configuration
global:
  ## Edition configuration - determines whether to deploy CE or EE version
  ## Valid values: "ce", "ee"
  edition: "ee"

  image:
    ## List of Kubernetes secrets to use for pulling images (e.g., for private registry).
    pullSecrets: [ ]
    ## Specify path prefix of images, no need to add the final slash `/`
    ## You can add namespace follow registry.
    ## eg: docker.io/minio/minio:latest ==> {{ registry }}/minio/minio:latest
    registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
    ## Only for csghub_server
    ## Specifies the csghub_server repository name.
    # name: "csghub_server"
    ## Specifies the csghub_server image version base (without ce/ee suffix).
    ## The actual tag will be constructed as: {{ tag }}-{{ edition }}
    ## Example: v1.8.0 will become v1.8.0-ce or v1.8.0-ee based on edition
    tag: "v1.8.0"
    ## Specifies the csghub_server image pull policy.
    pullPolicy: "IfNotPresent"

  ## Enable pdb to ensure application access continuity
  pdb:
    create: false
    minAvailable: 1
    # maxUnavailable: 1

  ## Define the access domain name of csghub and whether to enable encrypted access.
  ingress:
    ## Specify ingressClassName
    # className: "nginx"
    ## Ideally, it is best to have a registered domain name, +
    ## because local domain names may require some additional configuration
    domain: example.com
    ## According to the characteristics of the program, +
    ## it is not allowed to configure whether to enable TLS for each subChart separately, +
    ## but subCharts are allowed to define their own TLS secretName.
    tls: { }
      # enabled: false
      ## Specify the tls secret containing the wildcard domain name certificate. +
      ## You need to prepare certificates and create TLS secrets yourself.
      # secretName: ""
    service:
      ## `&type` is an internal anchor, do not delete it
      ##  `ingress-nginx-controller` and `kourier` will also the same
      ## Enums `LoadBalancer`、`NodePort`
      type: &type LoadBalancer

  ## This section configures whether to use an external container image repository.
  registry:
    ## Indicates whether the registry is external (true) or internal (false)
    external: false
    ## If encrypt with TLS/SSL
    insecure: false
    ## If `external` set to `true`, `connection` is needed.
    connection: { }
      ## The URL of the Docker registry service; default is Docker Hub
      # repository: "docker.io"
      ## The namespace (or organization) under which the images are stored in the registry
      # namespace: "csghub"
      ## The username for authenticating to the registry
      # username: ""
      ## The password for authenticating to the registry
      # password: ""

  ## This section configures whether to use an external postgresql database.
  postgresql:
    ## Specifies whether to use an external PostgreSQL instance or a built-in one
    external: false
    ## Configuring external postgresql database
    ## If `external` set to `true`, `connection` is needed.
    connection: { }
      ## Host of the PostgreSQL database. Leave empty for built-in PostgreSQL, or provide the external host address.
      # host: ""
      ## Port on which the PostgreSQL database is running. Default is usually 5432.
      # port: ""
      ## Prefix of the database to connect to.
      ## Priority:
      ##       postgresql.database > subChart.postgresql{}
      ## This method will store all metadata data in the same database, which is not recommended,
      ##   as it may cause data table conflicts.
      ## The recommended way is to comment this parameter and use the default database:
      ##   - csghub_portal
      ##   - csghub_server
      ##   - csghub_casdoor
      ##   - csghub_temporal
      ##   - csghub_temporal_visibility
      ##   - csghub_dataflow
      ##   - starship_codegpt
      ## Please make sure that the above databases have been created
      # database: ""
      ## Username for authenticating with the PostgreSQL database.
      # user: ""
      ## Password for the PostgreSQL user specified above.
      # password: ""
      ## Timezone for PostgreSQL
      # timezone: "Etc/UTC"

  ## This section configures whether to use an external redis cache.
  redis:
    ## Set to false to use internal Redis service
    external: false
    ## Configuring external redis cache
    ## If `external` set to `true`, `connection` is needed.
    connection: { }
      ## Host for accessing Redis
      # host: ""
      ## Port for accessing Redis
      # port: ""
      ## Password for accessing Redis
      # password: ""

  ## The following is a unified object storage configuration.
  ## Once configured, the configured object storage is automatically used instead of the built-in minio object storage.
  ## If you use external object storage, make sure the bucket has been created.
  objectStore:
    ## If you use external object storage, set this to true, otherwise set this to false to use built-in object storage.
    external: false
    ## Configuring external object storage
    ## If `external` set to `true`, `connection` is needed.
    connection: { }

  ## Global initContainers configuration
  ## These settings apply to all services unless overridden at the service level
  initContainers:
    ## Wait for Redis to be ready
#    waitForRedis:
#      enabled: true
#      image: "redis:7.2.5"
#      pullPolicy: "IfNotPresent"

    ## Wait for PostgreSQL to be ready
#    waitForPostgresql:
#      enabled: true
#      image: "opencsg/psql:latest"
#      pullPolicy: "IfNotPresent"

    ## Wait for Gitaly to be ready
#    waitForGitaly:
#      enabled: true
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"

    ## Wait for NATS to be ready
#    waitForNats:
#      enabled: true
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"

    ## Wait for Temporal to be ready
#    waitForTemporal:
#      enabled: true
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"

    ## Wait for Server to be ready (disabled by default, enable for dependent services)
#    waitForServer:
#      enabled: false
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"
#      service: "server"
      port: 8080

    ## Wait for Minio to be ready (disabled by default)
#    waitForMinio:
#      enabled: false
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"

    ## Wait for Casdoor to be ready (disabled by default)
#    waitForCasdoor:
#      enabled: false
#      image: "busybox:latest"
#      pullPolicy: "IfNotPresent"

  ## Global NetworkPolicy configuration
  networkPolicy:
    enabled: false
    ingressNamespace: "ingress-nginx"

  ## Global PodDisruptionBudget configuration
  podDisruptionBudget:
    enabled: false
      ## The endpoint of the object storage service, e.g., S3 compatible service URL
      # endpoint: ""
      ## The access key used for authentication
      # accessKey: ""
      ## The secret key used for authentication
      # accessSecret: ""
      ## The location used for authentication
      # region: "cn-north-1"
      ## Indicates whether to encrypt data in transit
      # encrypt: "true"
      ## Indicates whether to use path-style requests
      # pathStyle: "true"
      ## By default (in the case of comments), the following bucket is used:
      ##   - csghub-portal
      ##   - csghub-server
      ##   - csghub-registry
      ##   - csghub-workflow
      # bucket: ""

  ## Configuration for gitaly settings
  gitaly:
    ## If you use external gitaly, set this to true, otherwise set this to false to use built-in.
    external: false
    ## Configuring external object storage
    ## If `external` set to `true`, `connection` is needed.
    connection: { }
      ## The host where Gitaly is running
      # host: "csghub-gitaly"
      ## The port used to communicate with Gitaly
      # port: 8075
      ## The storage using by gitaly
      # storage: "default"
      ## The token for authenticating with Gitaly
      # token: ""
      ## If gitaly is a cluster
      # isCluster: false

  ## Configuration for content moderation settings
  moderation:
    enabled: false
    ## Configuring external content moderation server connection info
    connection: { }
      ## The access key ID for authentication with the moderation service
      # accessKeyId: ""
      ## The access key secret for authentication with the moderation service
      # accessKeySecret: ""
      ## The region where the moderation service is hosted
      # region: ""
  ## This section of configuration is used to integrate k8s to run various instances, +
  ## such as Space applications, fine-tuning, and inference instances.
  deploy:
    ## Enable or disable the deployment of this application.
    enabled: true
    ## Name of the Kubernetes Secret to be used for configuration or credentials.
    kubeSecret: "kube-configs"
    ## Kubernetes namespace where the deployment will be created.
    namespace: "spaces"
    ## If it's set to enable, it will install knative and argo automatically.
    autoConfigure: true
    ## Specifying this option can reduce the number of new namespaces.
    ## By default, 7 namespaces are created, which will be reduced to 4 after merging.
    mergingNamespace: false
    ## csghub uses knative serving to assist in instance construction, |
    ## so if this function is required, you need to configure the following information
    knative:
      serving:
        ## Configuration for Knative serving services.
        ## If `autoConfigure` set to true, following will also be configured to knative serving, unless already configured.
        services:
          ## Configure Knative Serving kourier.svc type
          ## If type is 'LoadBalancer', ensure your k8s can provide multiple loadBalancer addresses, or set to 'NodePort'
          - type: NodePort
            ## The hostname through which the service can be accessed.
            domain: "app.internal"
            ## The IP address assigned to the service, Usually remote k8s api-server node address.
            host: "*************"
            ## If type is 'LoadBalancer', port should be set to '80'
            ## If type is 'NodePort', it will be configured as the NodePort port of kourier.svc port 80
            port: "30213"
    ## Allow user defined pip source
    pipIndexUrl: "https://pypi.tuna.tsinghua.edu.cn/simple/"
    ## Image Builder configuration
    imageBuilder:
      ## If deployment.mergingNamespace == true, namespace ==> deployment.namespace
      namespace: "image-factory"
    usePublicDomain: true

  ## Configuration for Persistence
  persistence: { }
    ## Specifies the storage class to use for persistent storage
    # storageClass: ""
    ## Specifies the access mode for the persistent volume
    ## Options: ReadWriteOnce, ReadOnlyMany, ReadWriteMany
    # accessMode: ["ReadWriteOnce"]

  ## Starship GitLab OAuth
  starship:
    oauth: {}
      # issuer: "https://gitlab.example.com"
      # clientId: "73f1c2922f51d68fa87de8c1ef0e23e8940f3aa42f5ac7a55a1f586c597d7e9c"
      # clientSecret: "gloas-3ba02f02b3e993664ccdda2c4d76989caa11ce6f07a805b6872d266fbd465831"
  dataflow:
    enabled: false

  notification:
    extraConfig: {}

## More subChart configuration mappings are omitted here.
## Under normal circumstances, these configurations are not necessary.
## If necessary, you can check which parameters the subChart can configure and modify them.
## However, it should be noted that the current `autoscaling` is not adapted.

## The following configuration is just an example. Normally, +
## no modification is required and the default configuration will be applied.

## For Gitaly
gitaly:
  enabled: true
  ## Log output level
  logging:
    level: "info"
  ## Persistence settings for the Gitaly data.
  persistence:
    ## Specifies the StorageClass used for provisioning the volume.
    ## An empty value means the default StorageClass is used.
    ## StorageClass defines the type of storage used and can affect performance and cost.
    storageClass: ""
    ## Defines the access modes of the volume.
    ## ReadWriteOnce means the volume can be mounted as read-write by a single node.
    ## This is suitable for most use cases where a single instance of Gitaly is running.
    accessMode: [ "ReadWriteOnce" ]
    ## Specifies the size of the persistent volume.
    ## This should be adjusted based on expected usage and data growth over time.
    size: 200Gi

## For PostgreSQL
postgresql:
  ## Specify the database parameters that need to be optimized here.
  ## Under normal circumstances, the configuration can be automatically applied.
  parameters: { }
  ## List of databases to be created upon deployment
  # databases:
  #   - csghub_portal
  #   - csghub_server
  #   - csghub_casdoor
  #   - csghub_temporal
  #   - csghub_temporal_visibility
  #   - csghub_dataflow
  #   - starship_codegpt

## For Starship
## Note: Starship is only available in EE edition
starship:
  ## Starship will only be deployed when BOTH conditions are met:
  ## 1. global.edition is set to "ee" (Enterprise Edition)
  ## 2. starship.enabled is explicitly set to true
  ## If global.edition is "ce", starship will never be deployed regardless of this setting
  enabled: false

## For Dataflow
dataflow:
  ## You can only define configuration here， Control whether enabled in global.dataflow
  ## Configure AI Service
  openAI: { }
    ## The API endpoint address, e.g., "https://api.openai.com/v1/"
    # endpoint: ""
    ##  Your OpenAI API key used for authentication
    # apiKey: ""
    ## The version of the API being used, e.g., "v1"
    # apiVersion: ""
    ## The name of the model you want to use, e.g., "text-davinci-003"
    # model: ""

## CSGHub Application Components Configuration
## These components are integrated into the main chart, keeping their directory structure
## under charts/ for organization but no longer defined as subcharts

## CSGHub Server - Main API server
server:
  enabled: true
  replicas: 1
  image:
    repository: "csghub_server"
    pullPolicy: "IfNotPresent"
    pullSecrets: []
  service:
    type: ClusterIP
    port: 8080
    annotations: {}
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 500m
      memory: 1Gi
  environments: {}
  nodeSelector: {}
  tolerations: []
  affinity: {}
  podSecurityContext: {}
  securityContext: {}
  serviceAccount:
    create: false
    automount: false
  objectStore:
    directUpload: false
  autoscaler:
    enabled: false
    minReplicas: 1
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  job:
    enabled: false
    command: []
    args: []
    environments: {}
    resources: {}
  networkPolicy:
    enabled: false
    ingress: []
    egress: []
  podDisruptionBudget:
    enabled: false
    minAvailable: null
    maxUnavailable: null
  ## InitContainers configuration for server
  ## Inherits from global.initContainers but can be overridden here
  initContainers:
    configMapName: "server"  # ConfigMap name for environment variables
    # All other settings inherit from global.initContainers
    # You can override specific initContainers here if needed:
    # waitForRedis:
    #   enabled: true
    # waitForPostgresql:
    #   enabled: true
    # waitForGitaly:
    #   enabled: true
    # waitForNats:
    #   enabled: true
    # waitForTemporal:
    #   enabled: true

## User Management Service
user:
  enabled: true
  replicas: 1
  image:
    repository: "csghub_server"
    pullPolicy: "IfNotPresent"
    pullSecrets: []
  service:
    type: ClusterIP
    port: 8080
    annotations: {}
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 200m
      memory: 512Mi
  environments: {}
  nodeSelector: {}
  tolerations: []
  affinity: {}
  podSecurityContext: {}
  securityContext: {}
  serviceAccount:
    create: false
    automount: false
  ## InitContainers configuration for user service
  initContainers:
    configMapName: "server"  # Use server configmap for database connection
    # Enable waiting for server to be ready
    waitForServer:
      enabled: true
      service: "server"
      port: 8080
    # Disable infrastructure dependencies (server handles them)
    waitForRedis:
      enabled: false
    waitForPostgresql:
      enabled: false
    waitForGitaly:
      enabled: false
    waitForNats:
      enabled: false
    waitForTemporal:
      enabled: false
  autoscaler:
    enabled: false
    minReplicas: 1
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80
  job:
    enabled: false
  networkPolicy:
    enabled: false
  podDisruptionBudget:
    enabled: false

## For Minio
minio:
  ## Specify the bucket to be created and whether to enable version control.
  buckets:
    ## Default `false`
    versioning: false

## In order to make the maintenance of csghub helm easier, the official ingress-nginx is directly referenced here as a subChart.
## This section of configuration is added for better adaptation, but this section of configuration is not within your modification scope.
## Unless you know what you are doing, please do not modify this section of configuration.
## PLEASE DO NOT UPDATE!!!
ingress-nginx:
  ## Enable the NGINX Ingress Controller
  enabled: true
  ## TCP services configuration
  tcp:
    ## Forward TCP traffic on port 22 to the specified service
    22: csghub/csghub-gitlab-shell:22
  ## NGINX Ingress Controller configuration
  controller:
    ## Configuration for the controller image
    ## Reset digest to use third-party repository
    ## DO NOT UPDATE!!! image.digest and admissionWebhooks.patch.image.digest
    image:
      ## Digest of the image for the controller
      digest: ""
    ## Configuration for admission webhooks
    admissionWebhooks:
      ## Patch settings for admission webhooks
      patch:
        ## Digest of the image for the admission webhook
        image:
          ## Digest of the image for the admission webhook
          digest: ""
    ## Configuration settings for the Ingress Controller
    config:
      ## Set the risk level for annotations; critical indicates high risk
      annotations-risk-level: Critical
#      http-snippet: |
#        limit_req_zone $binary_remote_addr zone=global:10m rate=20r/s;
#        limit_conn_zone $binary_remote_addr zone=addr:10m;
#      server-snippet: |
#        limit_req zone=global burst=40;
#        limit_conn addr 50;
    ## Allow the use of server snippets in annotations
    allowSnippetAnnotations: true
    ## Configuration for the service exposed by the Ingress Controller
    service:
      type: *type
      ## Node ports for HTTP and HTTPS traffic
      nodePorts:
        ## Node port for HTTP traffic
        http: 30080
        ## Node port for HTTPS traffic
        https: 30443
        ## Node port for TCP traffic
        tcp:
          ## Node port for TCP traffic on port 22
          22: 30022

## This section is used to configure how to collect pod logs in the current namespace.
## By default, they are directly output to the fluentd standard output in json format.
fluentd:
  enabled: false
  # Configuration for Fluentd file sources
  fileConfigs:
    # Configuration for sources
    01_sources.conf: |-
      <source>
        @type tail
        @id in_tail_container_logs
        @label @KUBERNETES
        path /var/log/containers/*.log
        pos_file /var/log/fluentd-containers.log.pos
        tag kubernetes.*
        read_from_head true
        <parse>
          @type json
        </parse>
        emit_unmatched_lines false
      </source>
      # expose metrics in prometheus format
      <source>
        @type prometheus
        bind 0.0.0.0
        port 24231
        metrics_path /metrics
      </source>
      <system>
        log_level debug
      </system>
    # Configuration for filters
    02_filters.conf: |-
      <label @KUBERNETES>
        <filter kubernetes.**>
          @type kubernetes_metadata
          @id filter_kube_metadata
          lookup_from_k8s_field true
          skip_namespace_metadata true
          skip_master_url true
        </filter>
        <filter kubernetes.var.log.containers.**>
          @type grep
          <regexp>
            key $.kubernetes.labels
            pattern /csghub/
          </regexp>
        </filter>
        <match kubernetes.var.log.containers.**fluentd**>
          @type null
        </match>
        <match **>
          @type relabel
          @label @DISPATCH
        </match>
      </label>
    # Configuration for dispatching logs
    03_dispatch.conf: |-
      <label @DISPATCH>
        <match **>
          @type relabel
          @label @OUTPUT
        </match>
      </label>
    # Configuration for outputs
    04_outputs.conf: |-
      <label @OUTPUT>
        <match **>
          @type stdout
          <format>
            @type json
            localtime true
          </format>
        </match>
      </label>

csghub:
  portal:
    ## Default values for CSGHub-Portal.
    ## This is a YAML-formatted file.
    ## Declare variables to be passed into your templates.

    ## Define deploy replicas
    replicas: 1

    ## Configuration for images, it can be overwritten by global.images
    image:
      ## List of image pull secrets.
      ## Used to pull Docker images from private repositories.
      ## This array is empty by default, meaning no secrets are required by default.
      pullSecrets: []
      ## Specify path prefix relative to docker.io
      ## eg: minio/minio:latest with prefix <registry>/minio/minio:latest
      ## No need to add the final slash `/`
      registry: "opencsg-registry.cn-beijing.cr.aliyuncs.com/opencsg_public"
      ## Specifies the location of the Registry Docker image in the registry.
      repository: "csghub_portal"
      ## Defines the specific version of the Registry image to use.
      ## The tag will be automatically suffixed with edition (ce/ee) based on global.edition
      tag: "v1.8.0"
      ## Determines how the image should be pulled from the registry.
      pullPolicy: "IfNotPresent"

    ## Configuration for ingress. If enabled, you can access Minio console by minio.<domain>
    ingress:
      ## Enable or disable ingress.
      enabled: true
      ## TLS settings for ingress.
      tls:
        ## Enable or disable TLS.
        ## It can be overwritten by global
        enabled: false
        ## Whether to enable tls encryption.
        ## If this configuration is enabled, a tls secret needs to be provided
        ## It cannot be overwritten by global
        secretName: ""
      annotations: { }

    service:
      ## This determines how the Server service is accessed within the cluster or from external sources.
      type: ClusterIP
      ## This is the network port where the Server service will listen for connections.
      port: 8090

    ## Specifies the location and credentials for accessing the external PostgreSQL database.
    postgresql:
      ## Specifies the host address of the PostgreSQL database server.
      host: ""
      ## Defines the port on which the PostgreSQL server is listening.
      port: 5432
      ## The username used for authentication with the PostgreSQL database.
      user: "csghub_portal"
      ## The password used for authentication with the PostgreSQL database.
      ## It is empty by default for security reasons and should be set through secure means.
      password: ""
      ## The name of the database to connect to on the PostgreSQL server.
      database: "csghub_portal"
      ## Sets the timezone for the database connection, ensuring time-based operations use the correct timezone.
      timezone: "Etc/UTC"

    ## Specifies the location and credentials for accessing the external Object Storage (OSS) service.
    objectStore:
      ## The endpoint URL of the object storage service.
      endpoint: ""
      ## The access key ID for authentication with the object storage service.
      ## This is empty by default and should be provided securely.
      accessKey: "minio"
      ## The secret access key for authentication with the object storage service.
      ## This is also empty by default and should be provided securely.
      accessSecret: ""
      ## The name of the bucket within the object storage service to be used.
      bucket: "csghub-portal"
      ## The region where the bucket is located within the object storage service.
      region: "cn-north-1"
      ## If encrypted with TLS
      encrypt: false
      ## When set to "true", the bucket name will be part of the URL path.
      ## For example, the URL will be in the format:
      ## http://<minio-server>/<bucket-name>/<object-key>
      pathStyle: "true"

    ## SMTP server host address (e.g., smtp.example.com)
    smtp:
      host: ""
      ## SMTP server port number (e.g., 465 for SSL, 587 for TLS)
      port: 465
      ## Username for SMTP authentication
      username: ""
      ## Password for SMTP authentication
      password: ""

    serviceAccount:
      ## Determines whether a service account should be created.
      create: false
      ## Controls whether the service account token should be automatically mounted.
      automount: false
      ## Allows for annotations to be added to the service account.
      annotations: {}

    ## podAnnotations: Allows you to add annotations to the pods. Annotations can be used to attach arbitrary -
    ## non-identifying metadata to objects. Tools and libraries can retrieve this metadata.
    podAnnotations: {}

    ## podLabels: Provides the ability to add labels to the pods. Labels are key/value pairs that are attached to objects, -
    ## such as pods, which can be used for the purposes of organization and to select subsets of objects.
    podLabels: {}

    ## podSecurityContext: Defines security settings for the entire pod. This can include settings like the user and group -
    ## IDs that processes run as, and privilege and access control settings.
    podSecurityContext: {}

    ## securityContext: Specifies security settings for a specific container within a pod. This can include settings such as -
    ## capabilities, security enhanced Linux (SELinux) options, and whether the container should run as privileged.
    securityContext: {}
      # capabilities:
      #   drop:
      #   - ALL
      # readOnlyRootFilesystem: true
      # runAsNonRoot: true
    # runAsUser: 1000

    ## environments: This section is reserved for defining environment variables for the Runner container.
    ## Environment variables can be used to customize the behavior of the Runner instance.
    ## For example, you might use environment variables to configure logging levels or to enable certain Runner features.
    ## This section is currently empty, indicating that no environment variables have been explicitly set.
    environments: {}

    ## annotations: This section allows you to add annotations to the Runner deployment.
    ## Annotations are key-value pairs that can be used to store additional metadata about the deployment.
    ## This can be useful for tools and applications that interact with your Kubernetes cluster, providing them with extra -
    ## information about your Runner instance.
    ## Like the environments section, this is also currently empty.
    annotations: {}

    ## The 'resources' section is used to define the compute resource requirements for the Runner container.
    ## Here, you can specify the minimum and maximum amount of CPU and memory that the container is allowed to use.
    ## Leaving this section empty means that no specific resource limits or requests are set for the Runner container.
    ## This approach can be beneficial in environments with limited resources, such as development or testing environments,
    ## where you might not want to enforce strict resource constraints.
    ## However, for production environments, it's recommended to uncomment and set these values to ensure that the Runner container
    ## has enough resources to operate efficiently and to prevent it from consuming too much of the available resources on the node.
    ## 'limits' specify the maximum amount of CPU and memory the container can use.
    ## 'requests' specify the minimum amount of CPU and memory guaranteed to the container.
    ## If these values are not set, the container could be terminated in a resource-constrained environment or it might not perform as expected.
    resources: {}
    #  limits:
    #    cpu: 2000m
    #    memory: 4096Mi
    #  requests:
    #    cpu: 500m
    #    memory: 512Mi

    ## nodeSelector: This section allows you to specify node labels for pod assignment.
    ## This is useful for ensuring that pods are only scheduled on nodes with specific labels.
    nodeSelector: {}

    ## tolerations: This section allows you to specify tolerations for the pods.
    ## Tolerations enable the pods to schedule onto nodes with matching taints.
    ## This is useful in scenarios where you want to ensure that certain workloads run on dedicated nodes.
    tolerations: []

    ## affinity: This section allows you to set rules that affect how pods are scheduled based on various criteria -
    ## including labels of pods that are already running on the node.
    ## Affinity settings can be used to ensure that certain pods are co-located in the same node, zone, etc., or to -
    ## spread pods across nodes or zones for high availability.
    affinity: {}

    ## autoscaling: This section configures the Horizontal Pod Autoscaler (HPA) for the Runner deployment.
    ## The HPA automatically scales the number of pods in a deployment, replication controller, replica set, or stateful -
    ## set based on observed CPU utilization.
    autoscaling:
      ## Determines whether autoscaling is enabled. Set to true to enable autoscaling.
      enabled: false
      ## The minimum number of replicas. The autoscaler will not scale below this number.
      minReplicas: 1
      ## The maximum number of replicas. The autoscaler will not scale above this number.
      maxReplicas: 5
      ## The target average CPU utilization (represented as a percentage) over all the pods. When the average CPU utilization -
      ## exceeds this threshold, the HPA will scale up.
      targetCPUUtilizationPercentage: 90
      ## Uncomment to enable scaling based on memory usage. This sets the target average memory utilization over all the pods.
      targetMemoryUtilizationPercentage: 90

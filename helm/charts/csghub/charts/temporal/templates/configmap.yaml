{{- /*
Copyright OpenCSG, Inc. All Rights Reserved.
SPDX-License-Identifier: APACHE-2.0
*/}}

{{- if .Values.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "common.names.custom" . }}
  namespace: {{ .Release.Namespace }}
  labels: {{ include "common.labels" . | nindent 4 }}
  annotations:
    resource.dependencies/deployments: |
      {{ include "common.names.custom" . }}
data:
  DB: "postgres12"
  DEFAULT_NAMESPACE_RETENTION: "7d"
  DBNAME: {{ include "csghub.postgresql.database" . }}
  DB_PORT: {{ include "csghub.postgresql.port" . | quote }}
  POSTGRES_SEEDS: {{ include "csghub.postgresql.host" . }}
  {{- $user := include "csghub.postgresql.user" . }}
  POSTGRES_USER: {{ include "csghub.postgresql.user" . }}
  {{- $password := include "postgresql.initPass" $user }}
  {{- $secret := (include "common.names.custom" (list . "postgresql")) -}}
  {{- $secretData := (lookup "v1" "Secret" .Release.Namespace $secret).data }}
  {{- if $secretData }}
  {{- $secretPassword := index $secretData $user }}
  {{- if $secretPassword }}
  {{- $password = $secretPassword | b64dec }}
  {{- end }}
  {{- end }}
  POSTGRES_PWD: {{ or (include "csghub.postgresql.password" .) $password }}
  VISIBILITY_DBNAME: {{ printf "%s_visibility" (include "csghub.postgresql.database" .) }}

  # Object Storage Connection Info for Workflow
  TEMPORAL_S3_ENDPOINT: {{ include "csghub.objectStore.endpoint" (dict "context" . "service" "workflow") | trimPrefix "http://" | trimPrefix "https://" }}
  {{- if include "csghub.objectStore.external" . }}
  TEMPORAL_S3_ACCESS_KEY_ID: {{ include "csghub.objectStore.accessKey" (dict "context" . "service" "workflow") }}
  TEMPORAL_S3_ACCESS_KEY_SECRET: {{ include "csghub.objectStore.accessSecret" (dict "context" . "service" "workflow") }}
  {{- end }}
  TEMPORAL_S3_BUCKET: {{ include "csghub.objectStore.bucket" (dict "context" . "service" "workflow") }}
  TEMPORAL_S3_REGION: {{ include "csghub.objectStore.region" (dict "context" . "service" "workflow") }}
  TEMPORAL_S3_ENABLE_SSL: {{ include "csghub.objectStore.encrypt" (dict "context" . "service" "workflow") | quote }}
  {{- if eq (include "csghub.objectStore.pathStyle" (dict "context" . "service" "workflow")) "true" }}
  TEMPORAL_S3_PATH_STYLE: "true"
  {{- else }}
  TEMPORAL_S3_PATH_STYLE: "false"
  {{- end }}
{{- end }}
dependencies:
- name: ingress-nginx
  repository: file://charts/ingress-nginx
  version: 4.12.0
- name: fluentd
  repository: file://charts/fluentd
  version: 0.5.2
- name: postgresql
  repository: file://charts/postgresql
  version: 15.10.0
- name: gitaly
  repository: file://charts/gitaly
  version: 17.5.0
- name: minio
  repository: file://charts/minio
  version: 2024.7.15
- name: redis
  repository: file://charts/redis
  version: 7.2.5
- name: registry
  repository: file://charts/registry
  version: 2.8.3
- name: coredns
  repository: file://charts/coredns
  version: 1.11.1
- name: nats
  repository: file://charts/nats
  version: 2.10.16
- name: temporal
  repository: file://charts/temporal
  version: 1.25.1
- name: casdoor
  repository: file://charts/casdoor
  version: 1.799.0
- name: gitlab-shell
  repository: file://charts/gitlab-shell
  version: 17.5.0
- name: dataflow
  repository: file://charts/dataflow
  version: 1.4.0
- name: starship
  repository: file://charts/starship
  version: 0.1.9
- name: watcher
  repository: file://charts/watcher
  version: 1.1.1
digest: sha256:9277916133a122e867328c60ca081cbb5f5ab31283136338e685a6c207f1c298
generated: "2025-06-24T14:26:28.26548+08:00"
